#app {
  background-color: aliceblue;
  width: 100dvw;
  height: 100dvh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

h1 {
  text-align: center;
  padding: 2rem;
}

.drop-zone {
  width: 80%;
  height: 70%;
  border: 2px dashed #ccc;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s;
}

.drop-zone.drag-over {
  background-color: #f0f0f0;
  border-color: #333;
}

.drop-zone p {
  font-size: 1.2rem;
  text-align: center;
}

.loading-spinner {
  border: 8px solid #fff; /* Light grey */
  border-top: 8px solid #3498db; /* Blue */
  border-radius: 50%;
  width: 60px;
  height: 60px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
