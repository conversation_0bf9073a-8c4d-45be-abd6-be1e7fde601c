name: ci

on:
  push:
    branches:
      - main
    paths-ignore:
      - README.md
      - README_CN.md
  pull_request:
    paths-ignore:
      - README.md
      - README_CN.md

jobs:
  build-web:
    runs-on: windows-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Set up Rust
      uses: actions-rust-lang/setup-rust-toolchain@v1
    - name: Set up Node
      uses: actions/setup-node@v4
      with:
        node-version: 22
        cache: 'npm'
    - name: Install dependencies
      run: npm install
    - name: Build
      run: npm run build:web
    - name: Upload artifact
      id: deployment
      uses: actions/upload-pages-artifact@v3
      with:
        path: './web/dist'

  deploy:
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    needs:
      - build-web
    permissions:
      pages: write
      id-token: write
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    steps:
      - name: Setup Pages
        uses: actions/configure-pages@v3
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4