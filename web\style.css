* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f2f2f2;
  color: #333;
  line-height: 1.6;
  padding: 1rem;
}

.container {
  background-color: #ffffff;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 900px;
  width: 100%;
}

h1 {
  color: #2c3e50;
  margin-bottom: 25px;
  font-size: 2.2em;
}

p {
  margin-bottom: 10px;
  color: #555;
}

input[type="text"],
input[type="file"] {
  width: 100%;
  padding: 10px;
  margin-bottom: 15px;
  border: 1px solid #dcdcdc;
  border-radius: 5px;
  font-size: 1em;
}

input[type="text"]::placeholder {
  color: #aaa;
}

input[type="file"] {
  padding: 0;
  overflow: hidden;
  cursor: pointer;
}

input[type="file"]::-webkit-file-upload-button,
input[type="file"]::file-selector-button {
  background-color: #007bff;
  color: white;
  padding: 10px 15px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1em;
  transition: background-color 0.2s ease;
}

input[type="file"]::-webkit-file-upload-button:hover,
input[type="file"]::file-selector-button:hover {
  background-color: #0056b3;
}

button {
  background-color: #28a745;
  color: white;
  padding: 12px 25px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1.1em;
  transition: background-color 0.2s ease;
  margin-top: 10px;
}

button:hover {
  background-color: #218838;
}

#status {
  margin-top: 20px;
  font-weight: bold;
  color: #007bff;
}

.links {
  margin-top: 30px;
}

.links a {
  display: inline-block;
  margin: 0 10px;
}

.links img {
  vertical-align: middle;
  transition: transform 0.2s ease;
}

.links img:hover {
  transform: scale(1.1);
}

#sizes-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 10px;
  margin-bottom: 20px;
  -webkit-user-select: none;
  user-select: none;
}

#sizes-checkboxes label {
  background-color: #e9ecef;
  padding: 8px 12px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  width: 100%;

  display: flex;
  justify-content: space-between;
}

#sizes-checkboxes label:hover {
  background-color: #dee2e6;
}

#sizes-checkboxes label span {
  width: 100%;
}

#sizes-checkboxes input[type="checkbox"] {
  margin-right: 5px;
  accent-color: #007bff;
  cursor: pointer;
}