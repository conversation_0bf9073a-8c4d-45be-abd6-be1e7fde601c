{"name": "img2ico", "version": "1.3.0", "type": "module", "description": "A tool for converting image to ICO format.", "repository": {"type": "git", "url": "git+https://github.com/nini22P/img2ico.git"}, "main": "dist/node.js", "types": "dist/node.d.ts", "exports": {".": {"node": "./dist/node.js", "browser": "./dist/browser.js", "default": "./dist/node.js"}}, "bin": {"img2ico": "dist/cli.js"}, "files": ["dist"], "scripts": {"build": "npm run build:wasm && rimraf dist && tsc && node copy-wasm.mjs", "build:wasm": "npm run build:wasm:node && npm run build:wasm:web", "build:wasm:node": "wasm-pack build ./wasm --target nodejs --out-dir ../src/wasm-node", "build:wasm:web": "wasm-pack build ./wasm --target web --out-dir ../src/wasm-web", "build:web": "npm run build:wasm && vite build web", "prepublishOnly": "npm run build", "web": "npm run build:wasm && vite web", "lint": "eslint"}, "dependencies": {"buffer": "^6.0.3", "commander": "^14.0.0", "jimp": "^1.6.0"}, "devDependencies": {"@types/node": "^24.2.0", "eslint": "^9.32.0", "globals": "^16.3.0", "rimraf": "^6.0.1", "typescript": "^5.4.5", "typescript-eslint": "^8.39.0", "vite": "^7.1.1", "vite-plugin-compression": "^0.5.1", "wasm-pack": "^0.13.1"}, "keywords": ["ico", "image", "converter", "cli", "wasm", "rust"], "author": "22", "license": "MIT"}